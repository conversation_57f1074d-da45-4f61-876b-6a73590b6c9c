#!/bin/bash
set -euo pipefail
IFS=$'\n\t'

# 原：
# BASE_PATH="/mnt/efs/production/bureau"
# DOCKER_PATH="${BASE_PATH}/medical-docker"

# 建议改成「只在未定义时赋默认值」：
BASE_PATH="${BASE_PATH:-/mnt/efs/production/bureau}"
DOCKER_PATH="${DOCKER_PATH:-${BASE_PATH}/medical-docker}"

# === 进入指定的项目目录 ===
into_directory() {
  local project="$1"
  local target_path="${BASE_PATH}/${project}"

  if [ -d "$target_path" ]; then
    cd "$target_path" || exit 1
  else
    echo "[ERROR] Directory not found: $target_path"
    exit 1
  fi
}

# === 检出指定 Git 标签 ===
checkout_tag() {
  local git_tag="$1"
  echo "[INFO] Fetching Git tags..."
  git fetch --tags

  echo "[INFO] Checking out tag: $git_tag"
  if git rev-parse --verify "$git_tag" >/dev/null 2>&1; then
    git checkout "$git_tag"
  else
    git checkout -b "$git_tag" "refs/tags/$git_tag"
  fi
}

# === 比较 composer 文件是否有变化 ===
diff_composer() {
  local base_commit="$1"

  if git diff --quiet "$base_commit" HEAD -- composer.json composer.lock; then
    echo "[INFO] No changes in composer.json or composer.lock"
    return 1
  else
    echo "[INFO] Changes detected in composer.json or composer.lock"
    return 0
  fi
}

# === 在容器中执行 composer install ===
update_composer() {
  local project_name="$1"
  echo "[INFO] Updating composer dependencies for: $project_name"

  cd "$DOCKER_PATH" || exit 1

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    composer install --no-interaction --optimize-autoloader
  "
  local exit_code=$?

  if [ "$exit_code" -eq 0 ]; then
    echo "[INFO] Composer install completed successfully"
  else
    echo "[ERROR] Composer install returned exit code: $exit_code"
    git status composer.lock
    exit $exit_code
  fi
}

# === 在容器中执行 add poll interval list（添加多设备共享功能名单）===
add_poll_interval() {
  local project_name="$1"
  local merchant_id="$2"
  local interval_ms="$3"

  cd "$DOCKER_PATH" || exit 1

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    php artisan merchant:passive-poll-interval --action=add --merchantId=${merchant_id} --value=${interval_ms}
  " < /dev/null

  local exit_code=$?

  if [ "$exit_code" -eq 0 ]; then
    echo "[INFO] Add poll interval completed successfully. Merchant ID: ${merchant_id}, Interval (ms): ${interval_ms}"
  else
    echo "[ERROR] Add poll interval failed with exit code: $exit_code, Merchant ID: ${merchant_id}, Interval (ms): ${interval_ms}"
    return "$exit_code"
  fi
}

# === 在容器中执行 list poll interval（展示多设备共享功能名单）===
list_poll_interval() {
  local project_name="$1"

  cd "$DOCKER_PATH" || exit 1

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    php artisan merchant:passive-poll-interval --action=list
  " < /dev/null

  local exit_code=$?

  if [ "$exit_code" -eq 0 ]; then
    echo "[INFO] List poll interval completed successfully"
  else
    echo "[ERROR] List poll interval failed with exit code: $exit_code"
    return "$exit_code"
  fi
}

# === 在容器中执行 add smart pharmacy（添加智能药房名单）===
add_smart_pharmacy() {
  local project_name="$1"
  local merchant_id="$2"
  local deal_relevant="$3"

  cd "$DOCKER_PATH" || exit 1

  # Build command based on deal_relevant
  local artisan_cmd="php artisan merchant:smart-pharmacy --action=add --merchantId=${merchant_id}"
  local deal_flag=""
  if [ "${deal_relevant}" = "1" ]; then
    artisan_cmd="${artisan_cmd} --dealRelevant=1"
    deal_flag="used --dealRelevant=1"
  else
    deal_flag="no --dealRelevant"
  fi

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    ${artisan_cmd}
  " < /dev/null

  local exit_code=$?

  if [ "$exit_code" -eq 0 ]; then
    echo "[INFO] Add smart pharmacy completed successfully (${deal_flag}). Merchant ID: ${merchant_id}"
  else
    echo "[ERROR] Add smart pharmacy failed (${deal_flag}) with exit code: $exit_code, Merchant ID: ${merchant_id}"
    return "$exit_code"
  fi
}

# === 在容器中执行 add offline ocr（添加离线ocr名单）===
add_offline_ocr() {
  local project_name="$1"
  local merchant_id="$2"

  cd "$DOCKER_PATH" || exit 1

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    php artisan merchant:offline-ocr --action=add --merchantId=${merchant_id}
  " < /dev/null

  local exit_code=$?

  if [ "$exit_code" -eq 0 ]; then
    echo "[INFO] Add offline ocr successed, Merchant ID: ${merchant_id}"
  else
    echo "[ERROR] Add offline ocr failed with exit code: $exit_code, Merchant ID: ${merchant_id}"
    return "$exit_code"
  fi
}

# === 在容器中执行 check target configuration（检查目标店铺的配置）===
check_target_configuration() {
  local project_name="$1"
  local service_type="$2"
  local merchant_id="$3"

  cd "$DOCKER_PATH" || exit 1

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    php artisan merchant:${service_type} --action=check --merchantId=${merchant_id}
  " < /dev/null

  local exit_code=$?

  if [ "$exit_code" -eq 0 ]; then
    echo "[INFO] Check ${service_type} completed successfully"
  else
    echo "[ERROR] Check ${service_type} failed with exit code: $exit_code"
    return "$exit_code"
  fi
}
