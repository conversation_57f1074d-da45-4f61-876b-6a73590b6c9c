# Define Providers
terraform {
  required_providers {
    alicloud = {
      source  = "aliyun/alicloud"
      version = "~> 1.220"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.5"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.2"
    }
  }
}

# Define Variables
variable "region" {
  description = "Alibaba Cloud region where the instance is located"
  type        = string
  default     = "cn-hongkong"
  validation {
    condition     = var.region != "" && can(regex("^[a-z]{2}-[a-z]+$", var.region))
    error_message = "Error: 'region' must be provided and must be a valid Alibaba Cloud region (e.g., 'cn-hongkong')."
  }
}

variable "instance_id" {
  description = "ID of the existing ECS instance"
  type        = string
  validation {
    condition     = var.instance_id != "" && can(regex("^i-[0-9a-z]{17}$", var.instance_id))
    error_message = "Error: 'instance_id' must be provided and must be a valid ECS instance ID (e.g., 'i-bp1234567890abcdef')."
  }
}

variable "action" {
  description = "Action to perform: start or stop"
  type        = string
  validation {
    condition     = contains(["start", "stop"], var.action)
    error_message = "Error: 'action' must be provided and must be either 'start' or 'stop'."
  }
}

# Configure the Alibaba Cloud Provider
provider "alicloud" {
  region = var.region
}

# Define a local-exec script to control the instance state
resource "null_resource" "instance_control" {
  triggers = {
    action      = var.action
    instance_id = var.instance_id
  }

  provisioner "local-exec" {
    command = <<EOT
if [ "${var.action}" = "start" ]; then
  aliyun ecs StartInstance --InstanceId ${var.instance_id} --region ${var.region}
  echo "Instance ${var.instance_id} is starting..."
  
  # Wait for instance to be running (polling approach)
  while true; do
    STATUS=$(aliyun ecs DescribeInstances --InstanceIds '["${var.instance_id}"]' --region ${var.region} --output json | jq -r '.Instances.Instance[0].Status')
    if [ "$STATUS" = "Running" ]; then
      echo "Instance ${var.instance_id} is running."
      break
    fi
    echo "Waiting for instance to start... Current status: $STATUS"
    sleep 10
  done
  
  # Get public IP address
  IP=$(aliyun ecs DescribeInstances --InstanceIds '["${var.instance_id}"]' --region ${var.region} --output json | jq -r '.Instances.Instance[0].PublicIpAddress.IpAddress[0] // empty')
  if [ -z "$IP" ] || [ "$IP" = "null" ]; then
    # Try to get EIP if no public IP
    EIP=$(aliyun ecs DescribeInstances --InstanceIds '["${var.instance_id}"]' --region ${var.region} --output json | jq -r '.Instances.Instance[0].EipAddress.IpAddress // empty')
    if [ -z "$EIP" ] || [ "$EIP" = "null" ]; then
      printf "" > "${path.module}/instance_ip.txt"
    else
      printf "%s" "$EIP" > "${path.module}/instance_ip.txt"
    fi
  else
    printf "%s" "$IP" > "${path.module}/instance_ip.txt"
  fi
  
elif [ "${var.action}" = "stop" ]; then
  aliyun ecs StopInstance --InstanceId ${var.instance_id} --region ${var.region}
  echo "Instance ${var.instance_id} is stopping..."
  
  # Wait for instance to be stopped (polling approach)
  while true; do
    STATUS=$(aliyun ecs DescribeInstances --InstanceIds '["${var.instance_id}"]' --region ${var.region} --output json | jq -r '.Instances.Instance[0].Status')
    if [ "$STATUS" = "Stopped" ]; then
      echo "Instance ${var.instance_id} is stopped."
      break
    fi
    echo "Waiting for instance to stop... Current status: $STATUS"
    sleep 10
  done
  
  printf "" > "${path.module}/instance_ip.txt"
fi
EOT
    when    = create
  }
}

# Read the IP from the file created by the provisioner
data "local_file" "instance_ip" {
  filename   = "${path.module}/instance_ip.txt"
  depends_on = [null_resource.instance_control]
}

# Output the public IP of the instance from the file
output "instance_public_ip" {
  description = "Public IP of the existing ECS instance"
  value       = data.local_file.instance_ip.content
}
