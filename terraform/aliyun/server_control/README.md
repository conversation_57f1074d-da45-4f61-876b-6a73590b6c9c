# 阿里云 ECS 实例控制 Terraform 模块

本模块基于 AWS 版本的服务器控制模块迁移而来，用于控制阿里云 ECS 实例的启动和停止操作。

## 主要功能

- 启动指定的 ECS 实例
- 停止指定的 ECS 实例
- 获取实例的公网 IP 地址（包括弹性公网 IP）
- 等待实例状态变更完成

## 使用方法

### 1. 准备工作

确保已安装并配置阿里云 CLI：

```bash
# 安装阿里云 CLI
curl -fsSL https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-amd64.tgz | tar -xzC /usr/local/bin

# 配置访问凭证
aliyun configure
```

### 2. 配置变量

复制示例配置文件并填入实际值：

```bash
cp terraform.tfvars.example terraform.tfvars
```

编辑 `terraform.tfvars` 文件：

```hcl
region      = "cn-hongkong"
instance_id = "i-j6c12345678901234"  # 替换为实际的 ECS 实例 ID
action      = "start"                # 或 "stop"
```

### 3. 执行操作

```bash
# 初始化 Terraform
terraform init

# 查看执行计划
terraform plan

# 应用配置
terraform apply

# 查看输出
terraform output
```

## 主要迁移变更点

### 1. Provider 变更

**AWS 版本：**
```hcl
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.region
}
```

**阿里云版本：**
```hcl
terraform {
  required_providers {
    alicloud = {
      source  = "aliyun/alicloud"
      version = "~> 1.220"
    }
  }
}

provider "alicloud" {
  region = var.region
}
```

### 2. 区域设置

- **AWS**: 使用 `us-east-1` 等格式
- **阿里云**: 使用 `cn-hongkong` 等格式，默认设置为中国香港

### 3. 实例 ID 格式

- **AWS**: `i-1234567890abcdef0` (8-17位十六进制)
- **阿里云**: `i-bp1234567890abcdef` (17位字母数字组合)

### 4. CLI 命令变更

**AWS CLI 命令：**
```bash
aws ec2 start-instances --instance-ids ${instance_id} --region ${region}
aws ec2 wait instance-running --instance-ids ${instance_id} --region ${region}
```

**阿里云 CLI 命令：**
```bash
aliyun ecs StartInstance --InstanceId ${instance_id} --region ${region}
# 使用轮询方式等待状态变更（阿里云 CLI 没有内置 wait 命令）
```

### 5. IP 地址获取

**AWS**: 直接从 `PublicIpAddress` 字段获取
**阿里云**: 需要检查 `PublicIpAddress.IpAddress[0]` 和 `EipAddress.IpAddress` 两个字段

### 6. 状态轮询

由于阿里云 CLI 没有类似 AWS 的 `wait` 命令，使用轮询方式检查实例状态：

```bash
while true; do
  STATUS=$(aliyun ecs DescribeInstances --InstanceIds '["${instance_id}"]' --region ${region} --output json | jq -r '.Instances.Instance[0].Status')
  if [ "$STATUS" = "Running" ]; then
    break
  fi
  sleep 10
done
```

## 注意事项

1. **依赖要求**: 需要安装 `aliyun` CLI 和 `jq` 工具
2. **认证配置**: 需要预先配置阿里云访问凭证
3. **网络配置**: 确保 ECS 实例已配置公网 IP 或弹性公网 IP
4. **权限要求**: 确保使用的 AccessKey 具有 ECS 实例的启停权限

## 输出

- `instance_public_ip`: 实例的公网 IP 地址（如果有的话）
