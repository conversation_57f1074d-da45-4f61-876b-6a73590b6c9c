# 阿里云 ECS 实例控制 Terraform 模块

本模块基于 AWS 版本的服务器控制模块迁移而来，用于控制阿里云 ECS 实例的启动和停止操作。

## 主要功能

- 启动指定的 ECS 实例
- 停止指定的 ECS 实例
- 获取实例的公网 IP 地址（包括弹性公网 IP）
- 等待实例状态变更完成

## 使用方法

### 1. 准备工作

确保已安装并配置阿里云 CLI：

**macOS 系统：**
```bash
# 方法1：使用 Homebrew（推荐）
brew install aliyun-cli

# 方法2：手动安装
curl -fsSL https://aliyuncli.alicdn.com/aliyun-cli-macos-latest-amd64.tgz | tar -xzC /usr/local/bin
```

**Linux 系统：**
```bash
# AMD64 架构
curl -fsSL https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-amd64.tgz | tar -xzC /usr/local/bin

# ARM64 架构
curl -fsSL https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-arm64.tgz | tar -xzC /usr/local/bin
```

**Windows 系统：**
```powershell
# 使用 PowerShell 下载并解压
Invoke-WebRequest -Uri "https://aliyuncli.alicdn.com/aliyun-cli-windows-latest-amd64.zip" -OutFile "aliyun-cli.zip"
Expand-Archive -Path "aliyun-cli.zip" -DestinationPath "C:\Program Files\Aliyun CLI"
# 将 C:\Program Files\Aliyun CLI 添加到系统 PATH 环境变量
```

**配置访问凭证（所有系统）：**
```bash
aliyun configure
```

### 2. 配置变量

复制示例配置文件并填入实际值：

```bash
cp terraform.tfvars.example terraform.tfvars
```

编辑 `terraform.tfvars` 文件：

```hcl
region      = "cn-hongkong"
instance_id = "i-j6c12345678901234"  # 替换为实际的 ECS 实例 ID
action      = "start"                # 或 "stop"
```

### 3. 解决网络连接问题（重要）

如果在执行 `terraform init` 时遇到网络超时错误，请使用阿里云镜像源：

**macOS / Linux 系统：**
```bash
# 复制镜像配置到用户目录
cp .terraformrc ~/.terraformrc

# 清理之前的下载尝试
rm -rf .terraform .terraform.lock.hcl

# 重新初始化
terraform init
```

**Windows 系统：**
```powershell
# 复制镜像配置到用户目录
Copy-Item .terraformrc $env:USERPROFILE\terraform.rc

# 清理之前的下载尝试
Remove-Item -Recurse -Force .terraform, .terraform.lock.hcl -ErrorAction SilentlyContinue

# 重新初始化
terraform init
```

> **注意**：Windows 系统中配置文件名为 `terraform.rc`（不带点），位置为 `%USERPROFILE%\terraform.rc`

### 4. 执行操作

```bash
# 初始化 Terraform（如果上面的网络问题已解决）
terraform init

# 查看执行计划
terraform plan

# 应用配置
terraform apply

# 查看输出
terraform output
```

## 主要内容

### 1. Provider 变更

**AWS 版本：**
```hcl
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.region
}
```

**阿里云版本：**
```hcl
terraform {
  required_providers {
    alicloud = {
      source  = "aliyun/alicloud"
      version = "~> 1.220"
    }
  }
}

provider "alicloud" {
  region = var.region
}
```

### 2. 区域设置

- **AWS**: 使用 `us-east-1` 等格式
- **阿里云**: 使用 `cn-hongkong` 等格式，默认设置为中国香港

### 3. 实例 ID 格式

- **AWS**: 8-17位十六进制
- **阿里云**: 17位字母数字组合

### 4. CLI 命令变更

**AWS CLI 命令：**
```bash
aws ec2 start-instances --instance-ids ${instance_id} --region ${region}
aws ec2 wait instance-running --instance-ids ${instance_id} --region ${region}
```

**阿里云 CLI 命令：**
```bash
aliyun ecs StartInstance --InstanceId ${instance_id} --region ${region}
# 使用轮询方式等待状态变更（阿里云 CLI 没有内置 wait 命令）
```

### 5. IP 地址获取

**AWS**: 直接从 `PublicIpAddress` 字段获取
**阿里云**: 需要检查 `PublicIpAddress.IpAddress[0]` 和 `EipAddress.IpAddress` 两个字段

### 6. 状态轮询

由于阿里云 CLI 没有类似 AWS 的 `wait` 命令，使用轮询方式检查实例状态：

```bash
while true; do
  STATUS=$(aliyun ecs DescribeInstances --InstanceIds '["${instance_id}"]' --region ${region} --output json | jq -r '.Instances.Instance[0].Status')
  if [ "$STATUS" = "Running" ]; then
    break
  fi
  sleep 10
done
```
