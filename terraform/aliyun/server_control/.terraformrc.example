# Terraform CLI Configuration File Example
# This file shows how to configure Terraform to use provider mirrors
# 
# To use this configuration:
# 1. Copy this file to your home directory as .terraformrc (Linux/macOS) or terraform.rc (Windows)
# 2. Modify the paths according to your local setup
# 3. Download the required providers to the specified mirror path

provider_installation {
  # Use a local filesystem mirror for alicloud provider
  filesystem_mirror {
    path    = "/usr/local/share/terraform/providers"
    include = ["aliyun/alicloud"]
  }
  
  # Use direct downloads for other providers
  direct {
    exclude = ["aliyun/alicloud"]
  }
}

# Alternative configuration for network mirrors
# provider_installation {
#   network_mirror {
#     url = "https://terraform-mirror.example.com/"
#   }
# }

# Logging configuration (optional)
# log_level = "INFO"
# log_path = "/tmp/terraform.log"
