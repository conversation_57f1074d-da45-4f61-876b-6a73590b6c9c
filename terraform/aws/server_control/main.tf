# Define Providers
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.5"
    }
  }
}

# Define Variables
variable "region" {
  description = "AWS region where the instance is located"
  type        = string
  validation {
    condition     = var.region != "" && can(regex("^[a-z]{2}-[a-z]+-[0-9]$", var.region))
    error_message = "Error: 'region' must be provided and must be a valid AWS region (e.g., 'us-east-1')."
  }
}

variable "instance_id" {
  description = "ID of the existing EC2 instance"
  type        = string
  validation {
    condition     = var.instance_id != "" && can(regex("^i-[0-9a-f]{8,17}$", var.instance_id))
    error_message = "Error: 'instance_id' must be provided and must be a valid EC2 instance ID (e.g., 'i-1234567890abcdef0')."
  }
}

variable "action" {
  description = "Action to perform: start or stop"
  type        = string
  validation {
    condition     = contains(["start", "stop"], var.action)
    error_message = "Error: 'action' must be provided and must be either 'start' or 'stop'."
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.region
}

# Define a local-exec script to control the instance state
resource "null_resource" "instance_control" {
  triggers = {
    action      = var.action
    instance_id = var.instance_id
  }

  provisioner "local-exec" {
    command = <<EOT
if [ "${var.action}" = "start" ]; then
  aws ec2 start-instances --instance-ids ${var.instance_id} --region ${var.region}
  echo "Instance ${var.instance_id} is starting..."
  aws ec2 wait instance-running --instance-ids ${var.instance_id} --region ${var.region}
  echo "Instance ${var.instance_id} is running."
  IP=$(aws ec2 describe-instances --instance-ids ${var.instance_id} --query 'Reservations[0].Instances[0].PublicIpAddress' --output text --region ${var.region})
  if [ "$IP" = "None" ]; then
    printf "" > "${path.module}/instance_ip.txt"
  else
    printf "%s" "$IP" > "${path.module}/instance_ip.txt"
  fi
elif [ "${var.action}" = "stop" ]; then
  aws ec2 stop-instances --instance-ids ${var.instance_id} --region ${var.region}
  echo "Instance ${var.instance_id} is stopping..."
  aws ec2 wait instance-stopped --instance-ids ${var.instance_id} --region ${var.region}
  echo "Instance ${var.instance_id} is stopped."
  printf "" > "${path.module}/instance_ip.txt"
fi
EOT
    when    = create
  }
}

# Read the IP from the file created by the provisioner
data "local_file" "instance_ip" {
  filename   = "${path.module}/instance_ip.txt"
  depends_on = [null_resource.instance_control]
}

# Output the public IP of the instance from the file
output "instance_public_ip" {
  description = "Public IP of the existing Windows instance"
  value       = data.local_file.instance_ip.content
}
