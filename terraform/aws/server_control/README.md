# AWS EC2 实例控制 Terraform 模块

本模块用于控制 AWS EC2 实例的启动和停止操作，提供简单易用的实例状态管理功能。

## 主要功能

- 启动指定的 EC2 实例
- 停止指定的 EC2 实例
- 获取实例的公网 IP 地址
- 等待实例状态变更完成

## 使用方法

### 1. 准备工作

确保已安装并配置 AWS CLI：

**macOS 系统：**
```bash
# 方法1：使用 Homebrew（推荐）
brew install awscli

# 方法2：使用官方安装程序
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /
```

**Linux 系统：**
```bash
# AMD64 架构
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# ARM64 架构
curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

**Windows 系统：**
```powershell
# 使用 PowerShell 下载并安装
Invoke-WebRequest -Uri "https://awscli.amazonaws.com/AWSCLIV2.msi" -OutFile "AWSCLIV2.msi"
Start-Process msiexec.exe -ArgumentList "/i AWSCLIV2.msi /quiet" -Wait
```

**配置访问凭证（所有系统）：**
```bash
aws configure
```

### 2. 配置变量

复制示例配置文件并填入实际值：

```bash
cp terraform.tfvars.example terraform.tfvars
```

编辑 `terraform.tfvars` 文件：

```hcl
region      = "us-east-1"
instance_id = "i-1234567890abcdef0"  # 替换为实际的 EC2 实例 ID
action      = "start"                # 或 "stop"
```

### 3. 执行操作

```bash
# 初始化 Terraform
terraform init

# 查看执行计划
terraform plan

# 应用配置
terraform apply

# 查看输出
terraform output
```

## 主要内容

### 1. Provider 配置

```hcl
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.5"
    }
  }
}

provider "aws" {
  region = var.region
}
```

### 2. 区域设置

- 使用标准 AWS 区域格式，如 `us-east-1`、`us-west-2`、`eu-west-1` 等

### 3. 实例 ID 格式

- **AWS**: 8-17位十六进制，格式为 `i-xxxxxxxxxxxxxxxxx`

### 4. CLI 命令

**启动实例：**
```bash
aws ec2 start-instances --instance-ids ${instance_id} --region ${region}
aws ec2 wait instance-running --instance-ids ${instance_id} --region ${region}
```

**停止实例：**
```bash
aws ec2 stop-instances --instance-ids ${instance_id} --region ${region}
aws ec2 wait instance-stopped --instance-ids ${instance_id} --region ${region}
```

### 5. IP 地址获取

```bash
aws ec2 describe-instances --instance-ids ${instance_id} \
  --query 'Reservations[0].Instances[0].PublicIpAddress' \
  --output text --region ${region}
```

### 6. 状态等待

AWS CLI 提供内置的 `wait` 命令，无需手动轮询：

```bash
# 等待实例运行
aws ec2 wait instance-running --instance-ids ${instance_id} --region ${region}

# 等待实例停止
aws ec2 wait instance-stopped --instance-ids ${instance_id} --region ${region}
```

## 注意事项

1. **依赖要求**: 需要安装 AWS CLI v2
2. **认证配置**: 需要预先配置 AWS 访问凭证（Access Key ID 和 Secret Access Key）
3. **网络配置**: 确保 EC2 实例已配置公网 IP 或弹性 IP
4. **权限要求**: 确保使用的 IAM 用户或角色具有以下权限：
   - `ec2:StartInstances`
   - `ec2:StopInstances`
   - `ec2:DescribeInstances`
5. **跨平台兼容**: 本模块支持 macOS、Linux 和 Windows 系统

## 使用示例

### 启动实例示例

```bash
# 设置变量
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=start"

# 查看输出的 IP 地址
terraform output instance_public_ip
```

### 停止实例示例

```bash
# 停止实例
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=stop"
```

## 常见问题

### Q: 如何处理没有公网 IP 的实例？
A: 如果实例没有公网 IP，输出将为空。您可以通过以下方式为实例分配公网 IP：
- 在启动实例时分配公网 IP
- 为实例关联弹性 IP (EIP)

### Q: 如何处理权限不足的错误？
A: 确保您的 AWS 凭证具有必要的 EC2 权限。可以使用以下 IAM 策略：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:StartInstances",
                "ec2:StopInstances",
                "ec2:DescribeInstances"
            ],
            "Resource": "*"
        }
    ]
}
```

### Q: 实例启动/停止失败怎么办？
A: 检查以下几点：
1. 实例 ID 是否正确
2. 实例是否存在于指定区域
3. 实例当前状态是否允许执行操作
4. AWS 凭证是否有效

## 输出

- `instance_public_ip`: 实例的公网 IP 地址（如果有的话）
