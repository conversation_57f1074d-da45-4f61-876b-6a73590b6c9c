# Terraform 基础设施管理

本文档提供了使用 Terraform 管理本项目基础设施资源的说明和指南。

## 📊 工作流程图

有关完整工作流程的可视化表示，请参见：**[工作流程图](workflow-diagram.md)**

## 概述

此目录中的 Terraform 配置旨在以模块化和可重用的方式管理特定的云资源。目前，主要用例是控制现有 AWS EC2 实例的状态（启动/停止）。

## 先决条件

在使用这些 Terraform 配置之前，您需要在本地机器上安装和配置以下工具。

### 1. Terraform 安装

执行配置文件需要 Terraform。

- **官方文档**: 您可以在 [Terraform 官方网站](https://learn.hashicorp.com/tutorials/terraform/install-cli) 上找到所有操作系统的详细安装说明。

- **示例（macOS 使用 Homebrew）**:
  ```sh
  brew tap hashicorp/tap
  brew install hashicorp/tap/terraform
  ```

### 2. AWS CLI 安装

配置文件中的 `local-exec` 提供程序依赖于 AWS 命令行界面 (CLI) 来对 AWS API 执行命令。

- **官方文档**: 请按照 [AWS CLI 用户指南](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-install.html) 中的说明进行安装。

## 目录结构

`terraform/` 目录按云提供商和资源类型分离配置。

```
terraform/
├── README.md                                    # 本文档
├── workflow-diagram.md                          # 可视化工作流程图
├──aliyun/                                       # Aliyun 特定配置
│   └── ...                                      # 其下内容基本类似不赘述
└── aws/                                         # AWS 特定配置
    └── server_control/                          # Windows & Linux EC2 管理
            ├── main.tf                          # 核心 Terraform 配置
            ├── terraform.tfvars.example         # 示例变量文件
            ├── terraform.tfvars                 # 实际变量（用户创建）
            ├── terraform.tfstate                # Terraform 状态文件
            ├── terraform.tfstate.backup         # 状态备份文件
            └── instance_ip.txt                  # 实例 IP 输出文件
```

### 组件说明
- **`aliyun/`**: 包含 Aliyun 特定的 Terraform 配置
- **`aws/`**: 包含 AWS 特定的 Terraform 配置
- **`server_control/`**: Windows & Linux EC2 实例管理模块
- **`main.tf`**: 核心 Terraform 配置，包含提供程序设置、变量和资源
- **`terraform.tfvars.example`**: 变量配置模板
- **`instance_ip.txt`**: 包含当前实例公共 IP 地址的输出文件

### 主要功能
- **实例状态控制**: 启动或停止现有 EC2 实例（不创建新实例）
- **IP 地址管理**: 自动检索并存储实例公共 IP
- **变量验证**: 对区域、实例 ID 和操作参数进行内置验证
- **状态跟踪**: 用于基础设施跟踪的 Terraform 状态管理

## AWS 凭证配置

要允许 Terraform 与您的 AWS 账户交互，您必须配置 AWS 凭证。**永远不要在 `.tf` 文件中直接硬编码凭证。** 以下是推荐的方法，按优先级排序：

### 方法 1: 环境变量（推荐用于本地开发）

Terraform 自动检测并使用这些标准环境变量。这是本地使用的安全且直接的方法。

在您的终端中，导出以下变量：

```sh
export AWS_ACCESS_KEY_ID="YOUR_AWS_ACCESS_KEY"
export AWS_SECRET_ACCESS_KEY="YOUR_AWS_SECRET_KEY"

# 可选：设置默认区域
export AWS_DEFAULT_REGION="us-east-1"
```

**注意**: 这些变量仅为当前终端会话设置。要使其永久生效，请将它们添加到您的 shell 配置文件中（例如 `~/.zshrc`、`~/.bash_profile`）。

### 方法 2: 共享凭证文件（推荐用于本地开发）

AWS CLI 和 Terraform 可以共享凭证文件。这对于管理多个 AWS 配置文件是理想的。

1.  **位置**:
    -   **Linux/macOS**: `~/.aws/credentials`
    -   **Windows**: `C:\Users\<USER>\.aws\credentials`

2.  **格式**: 文件应为 INI 格式。对于默认配置文件，它看起来像这样：

    ```ini
    [default]
    aws_access_key_id = YOUR_AWS_ACCESS_KEY
    aws_secret_access_key = YOUR_AWS_SECRET_KEY
    ```

Terraform 将自动从 `[default]` 配置文件读取凭证。

### 方法 3: IAM 角色（AWS 环境的最佳实践）

当从 AWS EC2 实例运行 Terraform 时（例如，在 CI/CD 管道中），使用 IAM 角色是最安全的方法。

1.  创建具有必要权限的 IAM 角色。
2.  将角色附加到将运行 Terraform 的 EC2 实例。

Terraform 将自动从实例的元数据服务获取临时凭证，无需存储任何长期密钥。

## 使用方法

要运行实例控制脚本：

1.  **导航到目录**:
    ```sh
    cd terraform/aws/server_control
    ```

2.  **初始化 Terraform**:
    此命令下载所需的提供程序插件。每个项目只需运行一次。
    ```sh
    terraform init
    ```

3.  **应用配置**:
    使用 `terraform apply` 命令和变量来指定目标实例和所需操作。

    - **启动实例**:
      ```sh
      terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=start"
      ```

    - **停止实例**:
      ```sh
      terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=stop"
      ```

    将 `region` 和 `instance_id` 替换为您的实际值。Terraform 将在继续之前提示确认。

### 方法 2: 使用 `.tfvars` 文件（推荐）

为了更有组织的方法，您可以在 `.tfvars` 文件中定义变量。这避免了在命令行上传递变量。

1.  **创建变量文件**:
    在 `terraform/aws/server_control/` 目录中，创建名为 `terraform.tfvars` 的文件。您可以复制提供的示例文件：
    ```sh
    cp terraform.tfvars.example terraform.tfvars
    ```

2.  **编辑 `terraform.tfvars`**:
    打开 `terraform.tfvars` 文件并为您的特定实例和所需操作设置值。
    ```hcl
    region      = "us-east-1"
    instance_id = "i-your_actual_instance_id"
    action      = "start"
    ```

3.  **应用配置**:
    现在您可以运行 `apply` 而无需任何命令行变量。如果存在 `terraform.tfvars`，Terraform 会自动加载它。
    ```sh
    terraform apply
    ```
    Terraform 将审查计划并提示确认。