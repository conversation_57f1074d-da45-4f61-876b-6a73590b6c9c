# Ansible 自动化运维平台

本仓库包含一个基于 Ansible 的自动化平台，用于基础设施管理和性能测试。该平台支持多个项目环境，包括医疗后端运维和性能测试场景。

## 功能特性

- **多项目架构**: 为不同运维领域提供有组织的结构
- **基础设施自动化**: 完整的服务器设置和配置管理
- **性能测试框架**: 集成基于 Locust 的负载测试功能
- **安全集成**: 支持 Vault 加密敏感数据
- **Git 钩子集成**: 自动化验证和质量检查，包含 .pem 文件权限管理
- **容器集成**: 基于 Docker 的部署支持，配备 Semaphore UI
- **基础设施即代码**: Terraform 集成用于云资源管理
- **质量保证**: 全面的验证和测试框架

## 平台架构

```
ansible/
├── .githooks/                               # 自动化 Git 钩子
│   ├── post-checkout                       # 自动设置 .pem 文件权限
│   └── post-merge                          # 自动设置 .pem 文件权限
├── env/                                     # 环境配置
│   ├── Binary.md                           # Semaphore 二进制安装指南
│   ├── check_semaphore.sh                  # Semaphore 健康检查脚本
│   ├── docker-compose.yml                  # Docker 部署配置
│   └── semaphore.sh                        # Semaphore 管理脚本
├── projects/                               # 独立项目模块
│   ├── ansible.demo.cfg                    # 演示 Ansible 配置
│   ├── medical-backend/                    # 医疗后端运维
│   │   ├── roles/                          # 医疗专用角色
│   │   ├── inventory/                      # 医疗基础设施清单
│   │   ├── extra-vars/                     # 变量配置
│   │   ├── site.yml                       # 医疗部署剧本
│   │   ├── README.md                      # 医疗后端文档
│   │   └──  USAGE.md                       # 详细使用说明
│   └── performance/                       # 性能测试框架
│       ├── roles/                          # 性能测试角色
│       ├── extra-vars/                     # 测试场景配置
│       ├── inventory/                      # 性能测试清单
│       ├── site.yml                       # 性能测试剧本
│       └── README.md                      # 性能测试文档
├── terraform/                             # 基础设施即代码
│   ├── README.md                          # Terraform 文档
│   └── aws/                               # AWS 专用配置
│       └── windows/                       # Windows EC2 管理
├── test_gathering.yml                     # Ansible 事实收集测试
├── vault-vars.sh                          # Vault 加密工具
└── README.md                              # 本文档
```

## 先决条件

### 系统要求

- Ubuntu/Debian 基础的 Linux 系统
- Python 3.6 或更高版本
- 正确配置的 Git
- Docker 和 Docker Compose（用于 Semaphore UI）
- 对目标服务器的 SSH 访问权限

### 必需工具

1. **Ansible**: 基础设施自动化引擎
2. **Semaphore**: 基于 Web 的 Ansible UI（可选但推荐）
3. **Git**: 版本控制，集成钩子功能
4. **Docker**: 用于 Semaphore 部署的容器运行时
5. **Terraform**: 云资源管理的基础设施即代码工具（可选）

## 安装

### 安装 Ansible

```bash
# 添加 Ansible 仓库
sudo apt-add-repository ppa:ansible/ansible
sudo apt update

# 安装 Ansible
sudo apt install ansible

# 升级 Ansible（需要时）
sudo apt update
sudo apt upgrade ansible

# 验证安装
ansible --version
```

### 安装 Semaphore UI

Semaphore 提供基于 Web 的界面来管理 Ansible 操作。虽然平台支持完整的 Docker 部署，但推荐使用混合方法以获得更好的性能和资源管理。

#### 推荐的安装方法（混合）

为了获得最佳性能，建议仅对数据库层使用容器，并将 Semaphore 作为二进制文件运行：

1. **仅对数据库使用 Docker Compose**: 使用提供的 docker-compose 配置部署 PostgreSQL 数据库
2. **将 Semaphore 作为二进制文件运行**: 使用原生 Semaphore 二进制文件作为 Web 界面和执行引擎

**混合方法的优势：**
- 更好的性能和资源利用率
- 更容易的维护和更新
- 更灵活的配置选项
- 减少容器开销

👉 **[二进制安装指南](env/Binary.md)** - 二进制部署的完整设置说明

#### 完整 Docker 部署（替代方案）

如果您更喜欢完全容器化的设置，可以使用完整的 Docker Compose 配置：

```bash
# 创建自定义 docker 网络
sudo docker network create ${YOUR_NETWORK_NAME}

# 验证配置
sudo docker-compose config
# 或使用环境文件
sudo docker-compose --env-file .env config

# 启动 Semaphore（完整堆栈）
sudo docker-compose up -d
```

配置详情请参见：[docker-compose.yml](env/docker-compose.yml)

#### 仅数据库 Docker 部署（推荐）

对于混合方法，仅启动数据库服务：

```bash
# 创建自定义 docker 网络
sudo docker network create ${YOUR_NETWORK_NAME}

# 仅启动 PostgreSQL 数据库
sudo docker-compose up -d semaphore_db

# 验证数据库正在运行
sudo docker-compose ps semaphore_db
```

然后按照[二进制安装指南](env/Binary.md)使用容器化数据库设置 Semaphore 二进制文件。

## 配置

### Git 配置设置

确保 Git 钩子和文件权限的正确配置：

```bash
# 检查并设置 git filemode
git config --get core.filemode
# 如果不是 true，则设置它
git config core.filemode true

# 检查并设置 git hooks 路径
git config --get core.hooksPath
# 如果不是 .githooks，则设置它
git config core.hooksPath .githooks
```

**Git 钩子功能：**
- **自动 .pem 文件权限管理**: Post-checkout 和 post-merge 钩子自动为 SSH 密钥文件设置正确权限（600）
- **安全增强**: 确保 SSH 密钥在 Git 操作后得到适当保护
- **无缝集成**: 与正常的 Git 工作流程透明地工作

### Vault 配置

对于使用加密变量的项目，根据需要配置 vault 密码文件。可以使用 vault 加密工具 `vault-vars.sh` 来加密敏感配置文件。

## 使用方法

### 通用命令结构

> 📌 **重要提示：**
> 以下命令中的 `--vault-password-file VPF` 参数仅在使用 `vault-vars.sh` 加密了 `projects/<project_dir_name>/roles/*/vars/main.yml` 文件时才需要。否则，可以省略此参数！

### 发现命令

使用这些命令来探索和了解可用资源：

```bash
# 列出项目的所有目标主机
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-hosts

# 列出所有可用标签
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-tags

# 列出将要执行的所有任务
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-tasks
```

### 执行命令

执行完整或选择性任务的剧本：

```bash
# 执行项目中的所有任务
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF

# 使用标签执行特定任务
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -t <tag-name>

# 使用额外变量执行
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -e "variable=value"

# 使用详细输出进行调试
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -vvv
```

## 可用项目

### 医疗后端运维

医疗后端系统的完整基础设施自动化，包括：
- 服务器配置和部署
- 安全加固和合规性
- 应用程序部署自动化
- 健康监控和维护脚本
- 具有错误处理的强大脚本执行框架
- 全面的日志记录和备份管理

👉 **[医疗后端文档](projects/medical-backend/README.md)**
👉 **[详细使用指南](projects/medical-backend/USAGE.md)**
👉 **[验证报告](projects/medical-backend/VALIDATION_REPORT.md)**

#### 快速开始示例

```bash
# 在 Web 服务器上执行健康检查脚本
ansible-playbook projects/medical-backend/site.yml -e "script_name=health_check.sh target_group=web_servers"

# 部署后端服务
ansible-playbook projects/medical-backend/site.yml -t deploy_backend

# 运行维护脚本
ansible-playbook projects/medical-backend/site.yml -e "script_name=maintenance.sh target_group=all"

# 使用额外变量文件执行
ansible-playbook projects/medical-backend/site.yml -t execute_shell -e "@extra-vars/kenta.backend.yml"
```

### 性能测试框架

使用 Locust 的综合负载测试基础设施：
- 自动化测试环境部署
- 分布式负载测试能力
- 性能指标收集和分析
- 多种测试场景支持
- 测试工具的 Conda 环境管理
- 具有生成器节点的可扩展架构

👉 **[性能测试文档](projects/performance/README.md)**

#### 快速开始示例

```bash
# 部署完整的性能测试环境
ansible-playbook projects/performance/site.yml -t deploy_all

# 使用问卷场景运行负载测试
ansible-playbook projects/performance/site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# 使用演示场景运行负载测试
ansible-playbook projects/performance/site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-pres.yml"

# 仅准备基础设施
ansible-playbook projects/performance/site.yml -t play_prepare
```

### 基础设施即代码 (Terraform)

用于云资源管理的 Terraform 配置：
- AWS EC2 实例状态管理（启动/停止）
- 模块化和可重用的基础设施组件
- 支持多种云环境
- 与现有 Ansible 工作流程集成

👉 **[Terraform 文档](terraform/README.md)**

#### 快速开始示例

```bash
# 导航到 terraform 目录
cd terraform/aws/server_control
# 或
cd terraform/aliyun/server_control

# 初始化 Terraform
terraform init

# 以下启停实例的示例以 AWS EC2 为例
# 启动 EC2 实例
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=start"

# 停止 EC2 实例
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=stop"
```

### 测试和验证

确保平台可靠性的内置测试功能：
- Ansible 事实收集行为测试
- 语法验证和代码检查
- 项目特定的验证报告
- 通过 Git 钩子进行自动化质量检查

#### 测试示例

```bash
# 测试 Ansible 事实收集行为
ansible-playbook test_gathering.yml

# 验证项目语法
ansible-playbook projects/medical-backend/site.yml --syntax-check

# 检查清单配置
ansible-inventory --list -i projects/medical-backend/inventory/
```

## 项目开发指南

### 添加新项目

创建新项目时：

1. **遵循既定的目录结构**
2. **在项目 README 中创建全面的文档**
3. **实现适当的角色依赖关系**
4. **包含示例配置**和使用场景
5. **在开发环境中进行彻底测试**

### 角色开发标准

- 使用带有编号前缀的描述性角色名称（例如，`01-prepare`、`02-deploy`）
- 实现幂等操作
- 包含适当的错误处理和验证
- 记录所有变量及其用途
- 遵循 Ansible 安全最佳实践

### 变量管理

- 在项目间使用一致的命名约定
- 利用 vault 加密敏感数据
- 按范围组织变量（全局、角色特定、环境特定）
- 在适当的地方提供默认值

## 安全最佳实践

### 访问控制

- 使用 SSH 密钥进行身份验证而不是密码
- 实施最小权限访问原则
- 定期轮换 SSH 密钥和 vault 密码
- 监控和记录所有自动化活动

### 数据保护

- 使用 Ansible Vault 加密敏感变量
- 避免以明文形式存储凭据
- 为自动化流量使用安全网络
- 实施适当的备份和恢复程序

### 网络安全

- 为管理流量使用 VPN 或专用网络
- 为 Ansible 通信实施防火墙规则
- 监控网络流量异常
- 使用适当的身份验证保护 Semaphore UI

## 监控和日志

### 执行日志

- Ansible 执行日志存储在项目目录中
- Semaphore 提供基于 Web 的执行历史记录
- 使用详细模式进行详细故障排除
- 在大型部署期间监控系统资源

### 健康监控

- 使用自动化脚本实施定期健康检查
- 监控目标系统性能和可用性
- 为关键基础设施组件设置警报
- 维护部署和变更日志

## 故障排除

### 常见问题

1. **SSH 连接问题**
   - 验证 SSH 密钥和权限
   - 检查到目标主机的网络连接
   - 验证清单配置

2. **Vault 解密错误**
   - 确保正确的 vault 密码文件
   - 验证 vault 文件加密状态
   - 检查 vault 文件的文件权限

3. **角色依赖问题**
   - 验证角色路径和依赖关系
   - 检查循环依赖
   - 验证角色元数据配置

4. **性能问题**
   - 在执行期间监控系统资源
   - 优化剧本并行化设置
   - 检查到目标主机的网络延迟

### 调试模式

启用详细输出进行详细故障排除：

```bash
# 基本详细输出
ansible-playbook projects/<project>/site.yml -v

# 详细调试信息
ansible-playbook projects/<project>/site.yml -vvv

# 连接调试
ansible-playbook projects/<project>/site.yml -vvvv
```

### 验证命令

```bash
# 测试到所有主机的连接
ansible all -m ping -i projects/<project>/inventory/

# 检查 Ansible 配置
ansible-config dump

# 验证剧本语法
ansible-playbook projects/<project>/site.yml --syntax-check

# 执行试运行而不进行更改
ansible-playbook projects/<project>/site.yml --check
```

## 贡献

### 开发工作流程

1. **为新开发创建功能分支**
2. **遵循编码标准**和文档要求
3. **在开发环境中进行彻底测试**
4. **更新任何更改的文档**
5. **提交带有清晰描述的拉取请求**

### 代码质量

- 使用 Git 钩子进行自动化验证
- 遵循 Ansible 最佳实践和约定
- 实施全面的错误处理
- 包含适当的注释和文档
- 使用不同的操作系统版本进行测试

### 文档标准

- 保持 README 文件与任何更改同步更新
- 记录所有变量及其用途
- 提供清晰的使用示例
- 包含故障排除信息
- 维护版本兼容性说明

## 支持和资源

### 内部资源

- 各个 README 文件中的项目特定文档
- 用于自动化验证和质量检查的 Git 钩子
- 用于基于 Web 的管理和监控的 Semaphore UI

### 外部资源

- [Ansible 文档](https://docs.ansible.com/)
- [Ansible 最佳实践](https://docs.ansible.com/ansible/latest/user_guide/playbooks_best_practices.html)
- [Semaphore 文档](https://docs.semui.co/)
- [Docker 文档](https://docs.docker.com/)
- [Terraform 文档](https://www.terraform.io/docs/)
- [Locust 文档](https://docs.locust.io/)

### 获取帮助

1. **首先检查项目特定文档**
2. **查看故障排除部分**了解常见问题
3. **使用调试模式**获取详细错误信息
4. **使用提供的命令验证配置**
5. **监控系统日志**了解基础设施级别的问题